<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zmn.framework</groupId>
        <artifactId>framework-parent</artifactId>
        <version>9.1.0-RELEASE</version>
    </parent>

    <groupId>com.zmn</groupId>
    <artifactId>zmn-delivery-monitoring</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>zmn-delivery-monitoring-service</module>
        <module>zmn-delivery-monitoring-persistence</module>
        <module>zmn-delivery-monitoring-model</module>
        <module>zmn-delivery-monitoring-common</module>
        <module>zmn-delivery-monitoring-task</module>
        <module>zmn-delivery-monitoring-admin</module>
        <module>zmn-delivery-monitoring-manager</module>
    </modules>


    <properties>
        <revision>1.1.1-RELEASE</revision>
        <zmn.common.version>9.0.0-RELEASE</zmn.common.version>
        <zmn.consts.version>1.1.0-RELEASE</zmn.consts.version>
        <zmn.gms.dubbo.interface.version>1.3.10-RELEASE</zmn.gms.dubbo.interface.version>
        <zmn.base.engineer.dubbo.interface.version>1.1.84-RELEASE</zmn.base.engineer.dubbo.interface.version>
        <zmn.biz.engineer.dubbo.interface.version>1.1.58-RELEASE</zmn.biz.engineer.dubbo.interface.version>
        <zmn.engineer.plugin.version>1.2.26-RELEASE</zmn.engineer.plugin.version>
        <zmn.base.product.dubbo.interface.version>1.1.41-RELEASE</zmn.base.product.dubbo.interface.version>
        <zmn.engstock.dubbo.interface.version>1.1.12-RELEASE</zmn.engstock.dubbo.interface.version>
        <zmn.log.version>9.0.0-RELEASE</zmn.log.version>
        <zmn-biz-serv-work-pre-delivery-common.version>1.0.1-RELEASE</zmn-biz-serv-work-pre-delivery-common.version>
        <zmn-biz-serv-work-general-agg-dubbo.version>1.0.39-RELEASE</zmn-biz-serv-work-general-agg-dubbo.version>
        <zmn-base-serv-work-dubbo-interface.version>1.0.8-RELEASE</zmn-base-serv-work-dubbo-interface.version>
        <zmn.biz.serv.work.general.dubbo.interface.version>1.1.33-RELEASE</zmn.biz.serv.work.general.dubbo.interface.version>
        <zmn-biz-serv-work-distribute-dubbo-interface.version>1.0.65-RELEASE</zmn-biz-serv-work-distribute-dubbo-interface.version>
        <zmn-biz-serv-work-after-delivery-common.version>1.0.20-RELEASE</zmn-biz-serv-work-after-delivery-common.version>
        <zmn-biz-dtms-common.version>1.0.8-RELEASE</zmn-biz-dtms-common.version>
        <zmn-biz-serv-work-delivery-common.version>1.0.60-RELEASE</zmn-biz-serv-work-delivery-common.version>
        <zmn.tapi.dubbo.interface.version>1.16.11-RELEASE</zmn.tapi.dubbo.interface.version>
        <zmn.biz.bos.interface.version>1.0.14-RELEASE</zmn.biz.bos.interface.version>
        <zmn.mcc.dubbo.interface.version>1.5.33-RELEASE</zmn.mcc.dubbo.interface.version>
        <zmn-base-common-data-dubbo-interface.version>1.1.64-RELEASE</zmn-base-common-data-dubbo-interface.version>
        <zmn-biz-serv-order-pay-dubbo-interface.version>1.0.18-RELEASE</zmn-biz-serv-order-pay-dubbo-interface.version>
        <zmn.cube.dubbo.interface.version>3.1.1-RELEASE</zmn.cube.dubbo.interface.version>
        <zmn.biz.grade.dubbo.interface.version>1.0.4-RELEASE</zmn.biz.grade.dubbo.interface.version>
        <zmn.sp.dubbo.interface.version>1.1.2-RELEASE</zmn.sp.dubbo.interface.version>
        <zmn.base.pilot.dubbo.interface.version>1.2.2-RELEASE</zmn.base.pilot.dubbo.interface.version>
        <zmn.biz.strategy.dubbo.interface.version>2.0.7-RELEASE</zmn.biz.strategy.dubbo.interface.version>
        <zmn.biz.stoporder.dubbo.interface.version>1.0.17-RELEASE</zmn.biz.stoporder.dubbo.interface.version>
        <zmn-mcc-permit-client.version>3.0.0-RELEASE</zmn-mcc-permit-client.version>
        <zmn.base.cubird.dubbo.interface.version>1.0.8-RELEASE</zmn.base.cubird.dubbo.interface.version>
        <zmn.base.remind.common.version>1.5.0-RELEASE</zmn.base.remind.common.version>

        <!-- 跟单系统 -->
        <zmn.track.dubbo.interface.version>1.5.23-SNAPSHOT</zmn.track.dubbo.interface.version>
        <!-- 时效系统 -->
        <zmn.biz.aging.dubbo.interface.version>1.1.4-RELEASE</zmn.biz.aging.dubbo.interface.version>
        <!-- 小号 -->
        <zmn.xno.common.version>1.2.16-RELEASE</zmn.xno.common.version>
        <zmn.xno.dubbo.interface.version>1.2.13-RELEASE</zmn.xno.dubbo.interface.version>
        <zmn-base-serv-work-common.version>1.0.21-RELEASE</zmn-base-serv-work-common.version>
        <!--  base-oem-dubbo -->
        <zmn.base.oem.dubbo.interface.version>1.0.5-RELEASE</zmn.base.oem.dubbo.interface.version>
        <zmn.biz.twd.dubbo.interface.version>1.0.7-RELEASE</zmn.biz.twd.dubbo.interface.version>
        <!--  base-mcc-dubbo -->
        <zmn.base.mcc.dubbo.interface.version>1.1.5-RELEASE</zmn.base.mcc.dubbo.interface.version>
        <!-- zmn-biz-tm-common -->
        <zmn.biz.tm.boot.starter.version>2.0.2-RELEASE</zmn.biz.tm.boot.starter.version>
        <zmn.biz.abnormal.dubbo.interface.version>1.3.4-SNAPSHOT</zmn.biz.abnormal.dubbo.interface.version>
        <zmn.anole.dubbo.interface.version>1.0.2-RELEASE</zmn.anole.dubbo.interface.version>
        <zmn.biz.tms.version>1.0.28-RELEASE</zmn.biz.tms.version>

        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <org.projectlombok.mapstruct.version>0.2.0</org.projectlombok.mapstruct.version>
        <org.apache.skywalking>9.1.0</org.apache.skywalking>

        <hutool.version>5.5.8</hutool.version>
        <!-- 指定正常方式发布 -->
        <publish.normal>true</publish.normal>
        <!--指定瘦身方式发布-->
        <publish.thine>false</publish.thine>
    </properties>

    <dependencyManagement>

        <!-- interval artifactId -->
        <dependencies>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-delivery-monitoring-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-delivery-monitoring-persistence</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-delivery-monitoring-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-delivery-monitoring-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-delivery-monitoring-manager</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-common</artifactId>
                <version>${zmn.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-consts</artifactId>
                <version>${zmn.consts.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${org.apache.skywalking}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-common-data-dubbo-interface</artifactId>
                <version>${zmn-base-common-data-dubbo-interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-remind-common</artifactId>
                <version>${zmn.base.remind.common.version}</version>
            </dependency>

            <!-- gms -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-gms-dubbo-interface</artifactId>
                <version>${zmn.gms.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-work-pre-delivery-common</artifactId>
                <version>${zmn-biz-serv-work-pre-delivery-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-base-serv-work-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- mapstruct依赖包 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${org.projectlombok.mapstruct.version}</version>
            </dependency>


            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-work-general-agg-dubbo-interface</artifactId>
                <version>${zmn-biz-serv-work-general-agg-dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-base-serv-work-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-work-general-dubbo-interface</artifactId>
                <version>${zmn.biz.serv.work.general.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-serv-work-dubbo-interface</artifactId>
                <version>${zmn-base-serv-work-dubbo-interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-work-distribute-dubbo-interface</artifactId>
                <version>${zmn-biz-serv-work-distribute-dubbo-interface.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-biz-delivery-log-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-base-common-data-dubbo-interface</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-cubird-dubbo-interface</artifactId>
                <version>${zmn.base.cubird.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-work-after-delivery-dubbo-interface</artifactId>
                <version>${zmn-biz-serv-work-after-delivery-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-base-serv-work-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-dtms-common</artifactId>
                <version>${zmn-biz-dtms-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-work-after-delivery-common</artifactId>
                <version>${zmn-biz-serv-work-after-delivery-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-base-serv-work-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-work-delivery-common</artifactId>
                <version>${zmn-biz-serv-work-delivery-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-work-general-common</artifactId>
                <version>${zmn.biz.serv.work.general.dubbo.interface.version}</version>
            </dependency>
            <!-- engineer -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-engineer-dubbo-interface</artifactId>
                <version>${zmn.base.engineer.dubbo.interface.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-engineer-common</artifactId>
                <version>${zmn.base.engineer.dubbo.interface.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-engineer-dubbo-interface</artifactId>
                <version>${zmn.biz.engineer.dubbo.interface.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-permit-check-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-engineer-plugin</artifactId>
                <version>${zmn.engineer.plugin.version}</version>
            </dependency>

            <!-- base-product -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-product-dubbo-interface</artifactId>
                <version>${zmn.base.product.dubbo.interface.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-base-common-data-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-serv-order-pay-dubbo-interface</artifactId>
                <version>${zmn-biz-serv-order-pay-dubbo-interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-engstock-dubbo-interface</artifactId>
                <version>${zmn.engstock.dubbo.interface.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zmn</groupId>
                        <artifactId>zmn-framework-dubbo-consumer-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- log-client -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-log-client</artifactId>
                <version>${zmn.log.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/eu.bitwalker/UserAgentUtils -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>1.21</version>
            </dependency>

            <!-- tapi -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-tapi-dubbo-interface</artifactId>
                <version>${zmn.tapi.dubbo.interface.version}</version>
            </dependency>

            <!-- biz-bos -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-bos-dubbo-interface</artifactId>
                <version>${zmn.biz.bos.interface.version}</version>
            </dependency>

            <!-- biz-bos -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-bos-common</artifactId>
                <version>${zmn.biz.bos.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-mcc-dubbo-interface</artifactId>
                <version>${zmn.mcc.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-cube-dubbo-interface</artifactId>
                <version>${zmn.cube.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-grade-dubbo-interface</artifactId>
                <version>${zmn.biz.grade.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-grade-common</artifactId>
                <version>${zmn.biz.grade.dubbo.interface.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>zmn-biz-engineer-common</artifactId>
                        <groupId>com.zmn</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-mcc-permit-client</artifactId>
                <version>${zmn-mcc-permit-client.version}</version>
            </dependency>
            <!-- zmn-track -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-track-dubbo-interface</artifactId>
                <version>${zmn.track.dubbo.interface.version}</version>
            </dependency>
            <!-- zmn-track -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-track-common</artifactId>
                <version>${zmn.track.dubbo.interface.version}</version>
            </dependency>
            <!-- zmn-biz-aging -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-aging-dubbo-interface</artifactId>
                <version>${zmn.biz.aging.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- sp -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-sp-dubbo-interface</artifactId>
                <version>${zmn.sp.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-pilot-dubbo-interface</artifactId>
                <version>${zmn.base.pilot.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-strategy-dubbo-interface</artifactId>
                <version>${zmn.biz.strategy.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-stoporder-dubbo-interface</artifactId>
                <version>${zmn.biz.stoporder.dubbo.interface.version}</version>
            </dependency>

            <!-- xno -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-xno-common</artifactId>
                <version>${zmn.xno.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-xno-dubbo-interface</artifactId>
                <version>${zmn.xno.dubbo.interface.version}</version>
            </dependency>

            <!--  base-oem-dubbo -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-oem-dubbo-interface</artifactId>
                <version>${zmn.base.oem.dubbo.interface.version}</version>
            </dependency>

            <!--  zmn-biz-twd-dubbo -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-twd-dubbo-interface</artifactId>
                <version>${zmn.biz.twd.dubbo.interface.version}</version>
            </dependency>


            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-abnormal-dubbo-interface</artifactId>
                <version>${zmn.biz.abnormal.dubbo.interface.version}</version>
            </dependency>

            <!--  base-mcc-dubbo -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-mcc-dubbo-interface</artifactId>
                <version>${zmn.base.mcc.dubbo.interface.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>zmn-biz-tm-boot-starter</artifactId>
                        <groupId>com.zmn</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- zmn-biz-tm-common  -->
            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-tm-boot-starter</artifactId>
                <version>${zmn.biz.tm.boot.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-anole-dubbo-interface</artifactId>
                <version>${zmn.anole.dubbo.interface.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-biz-tms-dubbo-interface</artifactId>
                <version>${zmn.biz.tms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-base-serv-work-common</artifactId>
                <version>${zmn-base-serv-work-common.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>nexus</id>
            <url>https://maven.xiujiadian.com/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>com.zmn</groupId>
                <artifactId>zmn-generator-maven-plugin</artifactId>
                <version>2.0.0-SNAPSHOT</version>
                <!-- 阻止子模块继承 -->
                <inherited>false</inherited>
            </plugin>
        </plugins>
    </build>


</project>